#!/usr/bin/env coffee

# 测试 extractAddressFromOrigAddr 函数的修改
propAddress = require './src/lib/propAddress'

testCases = [
  {
    input: 'Clark Place Multi Family ($17,000 per Unit)'
    expected: 'Clark Place Multi Family ($17,000 per Unit)'
    description: '包含价格信息的描述性地址，应该完整保留'
  }
  {
    input: '430 Arlington Avenue, Toronto C03, ON M6C 3A2'
    expected: '430 Arlington Avenue'
    description: '标准地址+城市+省份+邮编格式，应该只保留地址部分'
  }
  {
    input: '511, 128 2 Street SW'
    expected: '128 2 Street SW'
    description: 'unit+地址格式，应该保留地址部分'
  }
  {
    input: '123 Main Street, Vancouver, BC V6B 1A1'
    expected: '123 Main Street'
    description: '另一个标准地址格式测试'
  }
  {
    input: 'Luxury Condo Building (CAD $2,500/month)'
    expected: 'Luxury Condo Building (CAD $2,500/month)'
    description: '包含CAD价格信息，应该完整保留'
  }
  {
    input: 'Simple Address Without Comma'
    expected: 'Simple Address Without Comma'
    description: '没有逗号的简单地址，应该完整保留'
  }
]

console.log '测试 extractAddressFromOrigAddr 函数修改...\n'

for testCase, index in testCases
  result = propAddress.extractAddressFromOrigAddr(testCase.input)
  success = result is testCase.expected
  
  console.log "测试 #{index + 1}: #{if success then '✓ 通过' else '✗ 失败'}"
  console.log "  描述: #{testCase.description}"
  console.log "  输入: '#{testCase.input}'"
  console.log "  期望: '#{testCase.expected}'"
  console.log "  实际: '#{result}'"
  console.log ""

console.log '测试完成!'
